const { addonBuilder, serveHTTP } = require('stremio-addon-sdk');
const fetch = require('node-fetch');
const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const PORT = process.env.PORT || 7000;
const IMDB_API_URL = 'https://rest.imdbapi.dev';
const TMDB_API_KEY = process.env.TMDB_API_KEY || 'f8870f4d48027b9f66c6b1a76efb965b'; // Replace with your TMDB API key or set as environment variable

// Path to Python scripts
const EXTRACTOR_SCRIPT = path.resolve(__dirname, './video_extractor.py');
const TMDB_TO_VIXSRC_SCRIPT = path.resolve(__dirname, './tmdb_to_vixsrc.py');

// Create a new addon builder
const builder = addonBuilder({
    id: 'org.vixsrc.addon',
    version: '1.0.0',
    name: 'Vixsrc Streams',
    description: 'Watch movies and TV shows from vixsrc.to',
    resources: ['stream'],
    types: ['movie', 'series'],
    idPrefixes: ['tt'], // IMDB ID prefix
    catalogs: [] // Empty array for catalogs
});

// Helper function to query IMDB API using GraphQL
async function getImdbInfo(imdbId) {
    try {
        const query = `{
            title(id: "${imdbId}") {
                id
                type
                primary_title
                start_year
            }
        }`;

        const response = await fetch(IMDB_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ query })
        });

        const data = await response.json();
        return data.data.title;
    } catch (error) {
        console.error('Error fetching IMDB info:', error);
        return null;
    }
}

// Helper function to convert IMDB ID to TMDB ID
async function convertImdbToTmdb(imdbId, type) {
    try {
        // Use the TMDB find API to convert IMDB ID to TMDB ID
        const response = await axios.get(`https://api.themoviedb.org/3/find/${imdbId}?api_key=${TMDB_API_KEY}&external_source=imdb_id`);
        
        // Extract the TMDB ID based on the content type
        if (type === 'movie' && response.data.movie_results.length > 0) {
            return response.data.movie_results[0].id;
        } else if ((type === 'tvSeries' || type === 'tvMiniSeries') && response.data.tv_results.length > 0) {
            return response.data.tv_results[0].id;
        }
        
        return null;
    } catch (error) {
        console.error('Error converting IMDB to TMDB ID:', error);
        return null;
    }
}

// Helper function to get vixsrc ID from TMDB ID using the Python script
async function getTmdbToVixsrcId(tmdbId, isTV, season = null, episode = null) {
    return new Promise((resolve, reject) => {
        // Check if the Python script exists
        if (!fs.existsSync(TMDB_TO_VIXSRC_SCRIPT)) {
            console.error(`TMDB to vixsrc script not found at ${TMDB_TO_VIXSRC_SCRIPT}`);
            return reject(new Error('TMDB to vixsrc script not found'));
        }
        
        // Prepare the command arguments
        const args = [
            TMDB_TO_VIXSRC_SCRIPT,
            tmdbId,
            '--api-key', TMDB_API_KEY
        ];
        
        if (isTV) {
            args.push('--tv');
            if (season !== null) args.push('--season', season);
            if (episode !== null) args.push('--episode', episode);
        }
        
        // Spawn a Python process to run the script
        const pythonProcess = spawn('python3', args);
        
        let output = '';
        
        // Collect data from stdout
        pythonProcess.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        // Handle errors
        pythonProcess.stderr.on('data', (data) => {
            console.error('Python script error:', data.toString());
        });
        
        // When the process exits
        pythonProcess.on('close', (code) => {
            if (code !== 0) {
                console.error(`Python script exited with code ${code}`);
                return reject(new Error(`Failed to convert TMDB ID to vixsrc ID (exit code: ${code})`));
            }
            
            try {
                // Parse the JSON output
                const result = JSON.parse(output);
                
                if (result.error) {
                    return reject(new Error(result.error));
                }
                
                resolve(result.vixsrc_id);
            } catch (error) {
                console.error('Error parsing Python script output:', error);
                reject(error);
            }
        });
    });
}

// Helper function to extract m3u8 URL using the Python script
async function getM3u8Url(vixsrcId, isTv = false, season = null, episode = null) {
    return new Promise((resolve, reject) => {
        // Check if the Python script exists
        if (!fs.existsSync(EXTRACTOR_SCRIPT)) {
            console.error(`Video extractor script not found at ${EXTRACTOR_SCRIPT}`);
            return reject(new Error('Video extractor script not found'));
        }
        
        // Prepare the command arguments
        const args = [
            EXTRACTOR_SCRIPT,
            vixsrcId,
            '--extract-only'
        ];
        
        if (isTv) {
            args.push('--tv');
            if (season !== null) args.push('--season', season);
            if (episode !== null) args.push('--episode', episode);
        } else {
            args.push('--movie');
        }
        
        console.log(`Running command: python3 ${args.join(' ')}`);
        
        // Spawn a Python process to run the script
        const pythonProcess = spawn('python3', args);
        
        let output = '';
        
        // Collect data from stdout
        pythonProcess.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        // Handle errors
        pythonProcess.stderr.on('data', (data) => {
            console.error('Python script error:', data.toString());
        });
        
        // When the process exits
        pythonProcess.on('close', (code) => {
            if (code !== 0) {
                console.error(`Python script exited with code ${code}`);
                return reject(new Error(`Failed to extract m3u8 URL (exit code: ${code})`));
            }
            
            // The last line of output should be the m3u8 URL
            const lines = output.trim().split('\n');
            const m3u8Url = lines[lines.length - 1];
            
            if (!m3u8Url || !m3u8Url.includes('.m3u8')) {
                console.error('No valid m3u8 URL found in output:', output);
                return reject(new Error('No valid m3u8 URL found'));
            }
            
            resolve(m3u8Url);
        });
    });
}

// Define stream handler
builder.defineStreamHandler(async ({ type, id }) => {
    console.log(`Received stream request for ${type} with id ${id}`);
    
    try {
        // Parse the ID to extract season and episode for TV shows
        let imdbId = id;
        let season = null;
        let episode = null;
        
        if (type === 'series') {
            // For TV shows, Stremio passes the ID in the format: tt1234567:1:2 (imdbId:season:episode)
            const parts = id.split(':');
            if (parts.length >= 3) {
                imdbId = parts[0];
                season = parseInt(parts[1], 10);
                episode = parseInt(parts[2], 10);
            }
        }
        
        // Get IMDB info
        const imdbInfo = await getImdbInfo(imdbId);
        if (!imdbInfo) {
            console.error(`Failed to get IMDB info for ${imdbId}`);
            return { streams: [] };
        }
        
        console.log(`Found IMDB info for ${imdbId}: ${JSON.stringify(imdbInfo)}`);
        
        // Determine if it's a TV show
        const isTV = imdbInfo.type === 'tvSeries' || imdbInfo.type === 'tvMiniSeries';
        
        // Convert IMDB ID to TMDB ID
        const tmdbId = await convertImdbToTmdb(imdbId, imdbInfo.type);
        if (!tmdbId) {
            console.error(`Failed to convert IMDB ID ${imdbId} to TMDB ID`);
            return { streams: [] };
        }
        
        console.log(`Converted IMDB ID ${imdbId} to TMDB ID ${tmdbId}`);
        
        // Get vixsrc ID from TMDB ID
        const vixsrcId = await getTmdbToVixsrcId(tmdbId, isTV, season, episode);
        if (!vixsrcId) {
            console.error(`Failed to get vixsrc ID for TMDB ID ${tmdbId}`);
            return { streams: [] };
        }
        
        console.log(`Got vixsrc ID ${vixsrcId} for TMDB ID ${tmdbId}`);
        
        // Get the m3u8 URL
        const m3u8Url = await getM3u8Url(vixsrcId, isTV, season, episode);
        if (!m3u8Url) {
            console.error(`Failed to get m3u8 URL for ${type} with vixsrc ID ${vixsrcId}`);
            return { streams: [] };
        }
        
        console.log(`Got m3u8 URL: ${m3u8Url}`);
        
        // Return the stream
        return {
            streams: [
                {
                    title: `Vixsrc - ${imdbInfo.primary_title}`,
                    url: m3u8Url,
                    behaviorHints: {
                        notWebReady: true // Indicates that the stream is not ready for web playback
                    }
                }
            ]
        };
    } catch (error) {
        console.error('Error in stream handler:', error);
        return { streams: [] };
    }
});

// Serve the addon
serveHTTP(builder.getInterface(), { port: PORT, hostname: '0.0.0.0' });

console.log(`Addon running at http://127.0.0.1:${PORT}`);
