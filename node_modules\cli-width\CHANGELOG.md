# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [2.2.1](https://github.com/knownasilya/cli-width/compare/v2.2.0...v2.2.1) (2020-04-14)


### Bug Fixes

* add more node versions to travis ([f7bc148](https://github.com/knownasilya/cli-width/commit/f7bc14846c2547769681bfc56afed3d0b04aa11e))
* Reduce nesting in index.js and add package-lock.json ([#14](https://github.com/knownasilya/cli-width/issues/14)) ([92d8d6b](https://github.com/knownasilya/cli-width/commit/92d8d6b8e4ce3702b12356c5427723005fccf9b8))
* update deprecated deps and change coverage script ([db06065](https://github.com/knownasilya/cli-width/commit/db0606592f8347eb9f35abdf87c570e1d731463c))

<a name="2.2.0"></a>
# [2.2.0](https://github.com/knownasilya/cli-width/compare/v2.1.1...v2.2.0) (2017-08-22)


### Features

* return default if env is 0 ([1833baf](https://github.com/knownasilya/cli-width/commit/1833baf)), closes [#9](https://github.com/knownasilya/cli-width/issues/9)



<a name="2.1.1"></a>
## [2.1.1](https://github.com/knownasilya/cli-width/compare/v2.1.0...v2.1.1) (2017-08-22)
