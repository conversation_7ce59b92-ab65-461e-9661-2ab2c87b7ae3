{"name": "inquirer", "version": "6.5.2", "description": "A collection of common interactive command line user interfaces.", "author": "<PERSON> <<EMAIL>>", "files": ["lib", "README.md"], "main": "lib/inquirer.js", "keywords": ["command", "prompt", "stdin", "cli", "tty", "menu"], "engines": {"node": ">=6.0.0"}, "devDependencies": {"chai": "^4.2.0", "chalk-pipe": "^2.0.0", "cmdify": "^0.0.4", "mocha": "^5.0.0", "mockery": "^2.1.0", "nyc": "^13.1.0", "sinon": "^7.1.1"}, "scripts": {"test": "nyc mocha test/**/* -r ./test/before", "posttest": "nyc report --reporter=text-lcov > ../../coverage/nyc-report.lcov", "prepublishOnly": "cp ../../README.md .", "postpublish": "rm -f README.md"}, "repository": "SBoudrias/Inquirer.js", "license": "MIT", "dependencies": {"ansi-escapes": "^3.2.0", "chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-width": "^2.0.0", "external-editor": "^3.0.3", "figures": "^2.0.0", "lodash": "^4.17.12", "mute-stream": "0.0.7", "run-async": "^2.2.0", "rxjs": "^6.4.0", "string-width": "^2.1.0", "strip-ansi": "^5.1.0", "through": "^2.3.6"}, "gitHead": "7d87f666042c67638d2e89bd4586d22f61e90130"}