#!/usr/bin/env python3

import argparse
import json
import sys
import requests
from bs4 import BeautifulSoup
import re

# Function to search vixsrc.to for a movie or TV show by title and year
# Modifico la funzione search_vixsrc per utilizzare l'ID TMDB direttamente
def search_vixsrc(title, year=None, is_tv=False, tmdb_id=None):
    try:
        # Se abbiamo un TMDB ID, proviamo prima ad accedere direttamente all'URL con quell'ID
        if tmdb_id:
            # Determina l'URL diretto basato sul tipo di contenuto
            if is_tv:
                direct_url = f"https://vixsrc.to/tv/{tmdb_id}/1/1/"
            else:
                direct_url = f"https://vixsrc.to/movie/{tmdb_id}/"
            
            # Invia la richiesta
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(direct_url, headers=headers)
            
            # Se la risposta è 200 OK, abbiamo trovato la pagina
            if response.status_code == 200:
                print(f"Found direct match using TMDB ID at {direct_url}", file=sys.stderr)
                return tmdb_id
            else:
                print(f"Direct access with TMDB ID failed (status code: {response.status_code}), falling back to search", file=sys.stderr)
        
        # Se l'accesso diretto fallisce o non abbiamo un TMDB ID, procediamo con la ricerca per titolo
        # Format the search query
        query = title
        if year:
            query = f"{title} {year}"
            
        # URL encode the query
        query = requests.utils.quote(query)
        
        # Determine the search URL based on content type
        if is_tv:
            search_url = f"https://vixsrc.to/search?keyword={query}&vrf=&vt=tv"
        else:
            search_url = f"https://vixsrc.to/search?keyword={query}&vrf=&vt=movie"
        
        # Send the request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(search_url, headers=headers)
        
        if response.status_code != 200:
            print(f"Error: Failed to search vixsrc.to (status code: {response.status_code})", file=sys.stderr)
            return None
        
        # Parse the HTML response
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Find all movie/show cards
        cards = soup.select('.flw-item')
        
        results = []
        for card in cards:
            try:
                # Extract the title
                title_elem = card.select_one('.film-name a')
                if not title_elem:
                    continue
                    
                result_title = title_elem.text.strip()
                
                # Extract the URL and ID
                url = title_elem.get('href', '')
                if not url:
                    continue
                    
                # Extract the ID from the URL
                if is_tv:
                    id_match = re.search(r'/tv/([0-9]+)(?:/\d+/\d+)?', url)
                else:
                    id_match = re.search(r'/movie/([0-9]+)', url)
                    
                if not id_match:
                    continue
                    
                result_id = id_match.group(1)
                
                # Extract the year if available
                year_elem = card.select_one('.fd-infor .fdi-item')
                result_year = None
                if year_elem:
                    year_match = re.search(r'(\d{4})', year_elem.text)
                    if year_match:
                        result_year = year_match.group(1)
                
                # Add to results
                results.append({
                    'id': result_id,
                    'title': result_title,
                    'year': result_year,
                    'url': url
                })
            except Exception as e:
                print(f"Error parsing result: {str(e)}", file=sys.stderr)
                continue
        
        # If we have results and a year was provided, try to find an exact match
        if results and year:
            for result in results:
                if result['year'] == str(year):
                    return result['id']
        
        # Otherwise, return the first result's ID
        if results:
            return results[0]['id']
        
        # Se non abbiamo trovato risultati ma abbiamo un TMDB ID, proviamo a usarlo direttamente
        if tmdb_id:
            print(f"No search results found, using TMDB ID {tmdb_id} directly", file=sys.stderr)
            return tmdb_id
        
        return None
    
    except Exception as e:
        print(f"Error searching vixsrc.to: {str(e)}", file=sys.stderr)
        # Se c'è un errore ma abbiamo un TMDB ID, proviamo a usarlo direttamente
        if tmdb_id:
            print(f"Search failed, using TMDB ID {tmdb_id} directly", file=sys.stderr)
            return tmdb_id
        return None

# Function to get movie/TV show details from TMDB
def get_tmdb_details(tmdb_id, api_key, is_tv=False):
    try:
        # Determine the API endpoint based on content type
        if is_tv:
            url = f"https://api.themoviedb.org/3/tv/{tmdb_id}?api_key={api_key}"
        else:
            url = f"https://api.themoviedb.org/3/movie/{tmdb_id}?api_key={api_key}"
        
        # Send the request
        response = requests.get(url)
        
        if response.status_code != 200:
            print(f"Error: Failed to get TMDB details (status code: {response.status_code})", file=sys.stderr)
            return None
        
        # Parse the JSON response
        data = response.json()
        
        # Extract the title and year
        if is_tv:
            title = data.get('name')
            year = None
            first_air_date = data.get('first_air_date')
            if first_air_date and len(first_air_date) >= 4:
                year = first_air_date[:4]
        else:
            title = data.get('title')
            year = None
            release_date = data.get('release_date')
            if release_date and len(release_date) >= 4:
                year = release_date[:4]
        
        return {
            'title': title,
            'year': year
        }
    
    except Exception as e:
        print(f"Error getting TMDB details: {str(e)}", file=sys.stderr)
        return None

# Main function to convert TMDB ID to vixsrc ID
def tmdb_to_vixsrc(tmdb_id, api_key, is_tv=False):
    # Get details from TMDB
    details = get_tmdb_details(tmdb_id, api_key, is_tv)
    
    if not details or not details['title']:
        print(f"Error: Failed to get details for TMDB ID {tmdb_id}", file=sys.stderr)
        return None
    
    # Search vixsrc.to for the title and year, passing the TMDB ID as fallback
    vixsrc_id = search_vixsrc(details['title'], details['year'], is_tv, tmdb_id)
    
    return vixsrc_id

# Command-line interface
def main():
    parser = argparse.ArgumentParser(description='Convert TMDB ID to vixsrc ID')
    parser.add_argument('tmdb_id', help='TMDB ID')
    parser.add_argument('--api-key', required=True, help='TMDB API key')
    parser.add_argument('--tv', action='store_true', help='Treat as TV show')
    parser.add_argument('--season', type=int, help='Season number for TV shows')
    parser.add_argument('--episode', type=int, help='Episode number for TV shows')
    
    args = parser.parse_args()
    
    vixsrc_id = tmdb_to_vixsrc(args.tmdb_id, args.api_key, args.tv)
    
    if vixsrc_id:
        # Output as JSON for easy parsing
        result = {
            'tmdb_id': args.tmdb_id,
            'vixsrc_id': vixsrc_id,
            'is_tv': args.tv,
            'season': args.season,
            'episode': args.episode
        }
        print(json.dumps(result))
        return 0
    else:
        print(json.dumps({'error': f"Failed to convert TMDB ID {args.tmdb_id} to vixsrc ID"}))
        return 1

if __name__ == "__main__":
    sys.exit(main())