# Stremio Addon Examples


### Examples using SDK

- [Hello World Addon](https://github.com/Stremio/addon-helloworld): also includes a step by step tutorial
- [IGDB Addon](https://github.com/Stremio/stremio-igdb-addon/tree/tutorial)

### Examples not using this SDK

- [PHP Addon Example & Tutorial](https://github.com/Stremio/stremio-php-addon-example)
- [<PERSON><PERSON> (PHP) Addon Template](https://github.com/rleroi/Stremio-Laravel)
- [Go Addon Example](https://github.com/Stremio/addon-helloworld-go)
- [Go Addon Examples Using Unofficial SDK](https://github.com/Deflix-tv/go-stremio/tree/master/examples)
- [Python Addon Example & Tutorial](https://github.com/Stremio/addon-helloworld-python)
- [Ruby Addon Example & Tutorial](https://github.com/Stremio/addon-helloworld-ruby)
- [C# Addon Example](https://github.com/Stremio/addon-helloworld-csharp)
- [Rust Addon Example Using Unofficial SDK](https://github.com/sleeyax/stremio-addon-sdk/tree/master/example-addon)
- [Node.js Express Addon Example & Tutorial](https://github.com/Stremio/addon-helloworld-express)
- [Node.js Express Addon Example Using User Data](./advanced.md)
- [IMDB Lists - Node.js Express Addon Using User Data and Ajax Calls](https://github.com/jaruba/stremio-imdb-list)
- [IMDB Watchlist - Node.js Express Addon Using User Data and Proxying Another Stremio Addon](https://github.com/jaruba/stremio-imdb-watchlist)
- [Jackett Addon - Node.js Express Addon Using User Data](https://github.com/BoredLama/stremio-jackett-addon)


### Guides

- [Official SDK guide](https://stremio.github.io/stremio-addon-guide/sdk-guide/prelude)
- [Official generic guide](https://stremio.github.io/stremio-addon-guide/basics)


### Video tutorials

- [Building a Stremio addon](https://www.youtube.com/watch?v=HqTkQeRKF-c&list=PLhslIqdUyoB-8olXVaYQxDLJIIOcSQU3H)
