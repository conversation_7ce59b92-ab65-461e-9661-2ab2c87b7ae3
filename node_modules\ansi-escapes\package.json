{"name": "ansi-escapes", "version": "3.2.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": "sindresorhus/ansi-escapes", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "devDependencies": {"ava": "*", "xo": "*"}}